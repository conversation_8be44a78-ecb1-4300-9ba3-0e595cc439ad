import React, { useEffect, useState } from 'react'
import { useNetworkStore } from '../stores/networkStore'
import LocalModelSetupWizard from './LocalModelSetupWizard'

interface NetworkStatusProps {
  className?: string
}

const NetworkStatus: React.FC<NetworkStatusProps> = ({ className = '' }) => {
  const [showSetupWizard, setShowSetupWizard] = useState(false)
  const {
    isOnline,
    isPrivateMode,
    localModelsAvailable,
    ollamaConnected,
    lmStudioConnected,
    toggleOnline,
    togglePrivateMode,
    checkLocalModels
  } = useNetworkStore()

  // Only check local models when private mode is enabled
  useEffect(() => {
    if (isPrivateMode) {
      checkLocalModels()

      // Set up periodic check every 30 seconds when in private mode
      const interval = setInterval(checkLocalModels, 30000)

      return () => clearInterval(interval)
    }
  }, [isPrivateMode, checkLocalModels])

  const getStatusText = () => {
    if (isPrivateMode) {
      return localModelsAvailable ? 'Offline' : 'Offline (No Local Models)'
    }
    return isOnline ? 'Online' : 'Offline'
  }

  const getStatusColor = () => {
    if (isPrivateMode) {
      return localModelsAvailable ? 'text-gray-600' : 'text-yellow-400'
    }
    return isOnline ? 'text-primary' : 'text-gray-400'
  }

  const getWifiIconColor = () => {
    if (isPrivateMode) {
      return 'text-gray-600' // Dark color when in private mode
    }
    return isOnline ? 'text-primary' : 'text-gray-400'
  }

  const getWifiIcon = () => {
    if (isPrivateMode || !isOnline) {
      return 'fa-solid fa-wifi-slash'
    }
    return 'fa-solid fa-wifi'
  }

  const getLocalModelStatus = () => {
    if (!localModelsAvailable) return null
    
    const providers = []
    if (ollamaConnected) providers.push('Ollama')
    if (lmStudioConnected) providers.push('LM Studio')
    
    return providers.length > 0 ? `Local: ${providers.join(', ')}` : null
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* Network Status Toggle */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <button
            onClick={toggleOnline}
            className="w-8 h-8 rounded-full bg-gray-700 hover:bg-gray-600 flex items-center justify-center transition-colors"
            title={isOnline ? 'Go Offline' : 'Go Online'}
          >
            <i className={`${getWifiIcon()} text-sm ${getWifiIconColor()}`}></i>
          </button>
          <div className="flex flex-col">
            <span className={`text-sm font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </span>
            {getLocalModelStatus() && (
              <span className="text-xs text-gray-400">
                {getLocalModelStatus()}
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Private Mode Toggle */}
      <div className="bg-gray-700/50 rounded-lg p-3">
        <div className="flex items-center justify-between mb-2">
          <span className="text-sm font-medium text-supplement1">Private Mode</span>
          <button
            onClick={togglePrivateMode}
            className={`
              relative inline-flex h-6 w-11 items-center rounded-full transition-colors
              ${isPrivateMode ? 'bg-secondary' : 'bg-gray-600'}
            `}
            title="Toggle Private Mode"
          >
            <span
              className={`
                inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                ${isPrivateMode ? 'translate-x-6' : 'translate-x-1'}
              `}
            />
          </button>
        </div>
        
        <div className="flex items-center gap-2">
          <i className="fa-solid fa-info-circle text-gray-400 text-xs"></i>
          <span className="text-xs text-gray-400">
            {isPrivateMode 
              ? 'Local models only. All documents stay private.' 
              : 'External models available. Data may be shared.'
            }
          </span>
        </div>

        {/* Local Models Status */}
        {isPrivateMode && (
          <div className="mt-2 pt-2 border-t border-gray-600">
            <div className="flex items-center gap-2 mb-1">
              <i className={`fa-solid fa-circle text-xs ${localModelsAvailable ? 'text-green-400' : 'text-red-400'}`}></i>
              <span className="text-xs font-medium text-supplement1">
                Local Models: {localModelsAvailable ? 'Available' : 'Not Found'}
              </span>
            </div>
            
            {localModelsAvailable ? (
              <div className="space-y-1">
                {ollamaConnected && (
                  <div className="flex items-center gap-2">
                    <i className="fa-solid fa-check text-xs text-green-400"></i>
                    <span className="text-xs text-gray-300">Ollama Connected</span>
                  </div>
                )}
                {lmStudioConnected && (
                  <div className="flex items-center gap-2">
                    <i className="fa-solid fa-check text-xs text-green-400"></i>
                    <span className="text-xs text-gray-300">LM Studio Connected</span>
                  </div>
                )}
              </div>
            ) : (
              <button
                onClick={() => setShowSetupWizard(true)}
                className="text-xs text-primary hover:text-primary/80 underline"
              >
                Setup Local Models
              </button>
            )}
          </div>
        )}
      </div>

      {/* Setup Wizard Modal */}
      <LocalModelSetupWizard
        isOpen={showSetupWizard}
        onClose={() => setShowSetupWizard(false)}
      />
    </div>
  )
}

export default NetworkStatus
